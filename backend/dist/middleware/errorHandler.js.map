{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,yDAAgD;AAEhD,6CAA0C;AAO1C,MAAa,WAAY,SAAQ,KAAK;IAC7B,UAAU,CAAS;IACnB,aAAa,CAAU;IAE9B,YAAY,OAAe,EAAE,aAAqB,+BAAW,CAAC,qBAAqB;QACjF,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAXD,kCAWC;AAEM,MAAM,YAAY,GAAG,CAC1B,KAAe,EACf,GAAY,EACZ,GAAa,EACb,KAAmB,EACb,EAAE;IACR,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC;IAChC,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,+BAAW,CAAC,qBAAqB,CAAC;IACzE,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,uBAAuB,CAAC;IAGzD,eAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE;QAC9B,KAAK,EAAE;YACL,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,UAAU;SACX;QACD,OAAO,EAAE;YACP,MAAM;YACN,GAAG;YACH,EAAE;YACF,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;SACjC;KACF,CAAC,CAAC;IAGH,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;IAE7D,MAAM,aAAa,GAAG;QACpB,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,CAAC;YACrE,GAAG,CAAC,aAAa,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;SAC7C;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,IAAI,EAAE,GAAG;KACV,CAAC;IAEF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7C,CAAC,CAAC;AAvCW,QAAA,YAAY,gBAuCvB;AAEF,SAAS,sBAAsB,CAAC,UAAkB;IAChD,QAAQ,UAAU,EAAE,CAAC;QACnB,KAAK,+BAAW,CAAC,WAAW;YAC1B,OAAO,aAAa,CAAC;QACvB,KAAK,+BAAW,CAAC,YAAY;YAC3B,OAAO,cAAc,CAAC;QACxB,KAAK,+BAAW,CAAC,SAAS;YACxB,OAAO,WAAW,CAAC;QACrB,KAAK,+BAAW,CAAC,SAAS;YACxB,OAAO,WAAW,CAAC;QACrB,KAAK,+BAAW,CAAC,QAAQ;YACvB,OAAO,UAAU,CAAC;QACpB,KAAK,+BAAW,CAAC,oBAAoB;YACnC,OAAO,kBAAkB,CAAC;QAC5B,KAAK,+BAAW,CAAC,iBAAiB;YAChC,OAAO,mBAAmB,CAAC;QAC7B;YACE,OAAO,uBAAuB,CAAC;IACnC,CAAC;AACH,CAAC"}