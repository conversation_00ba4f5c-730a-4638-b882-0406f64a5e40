"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = exports.CustomError = void 0;
const http_status_codes_1 = require("http-status-codes");
const logger_1 = require("../config/logger");
class CustomError extends Error {
    statusCode;
    isOperational;
    constructor(message, statusCode = http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.CustomError = CustomError;
const errorHandler = (error, req, res, _next) => {
    const { method, url, ip } = req;
    const statusCode = error.statusCode || http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR;
    const message = error.message || 'Internal Server Error';
    logger_1.logger.error('Error occurred:', {
        error: {
            message: error.message,
            stack: error.stack,
            statusCode,
        },
        request: {
            method,
            url,
            ip,
            userAgent: req.get('User-Agent'),
        },
    });
    const isDevelopment = process.env.NODE_ENV === 'development';
    const errorResponse = {
        success: false,
        error: {
            message: isDevelopment ? message : getGenericErrorMessage(statusCode),
            ...(isDevelopment && { stack: error.stack }),
        },
        timestamp: new Date().toISOString(),
        path: url,
    };
    res.status(statusCode).json(errorResponse);
};
exports.errorHandler = errorHandler;
function getGenericErrorMessage(statusCode) {
    switch (statusCode) {
        case http_status_codes_1.StatusCodes.BAD_REQUEST:
            return 'Bad Request';
        case http_status_codes_1.StatusCodes.UNAUTHORIZED:
            return 'Unauthorized';
        case http_status_codes_1.StatusCodes.FORBIDDEN:
            return 'Forbidden';
        case http_status_codes_1.StatusCodes.NOT_FOUND:
            return 'Not Found';
        case http_status_codes_1.StatusCodes.CONFLICT:
            return 'Conflict';
        case http_status_codes_1.StatusCodes.UNPROCESSABLE_ENTITY:
            return 'Validation Error';
        case http_status_codes_1.StatusCodes.TOO_MANY_REQUESTS:
            return 'Too Many Requests';
        default:
            return 'Internal Server Error';
    }
}
//# sourceMappingURL=errorHandler.js.map