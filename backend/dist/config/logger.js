"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const logLevel = process.env.LOG_LEVEL || 'info';
const isProduction = process.env.NODE_ENV === 'production';
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaString = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaString}`;
}));
const fileFormat = winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
const transports = [];
if (!isProduction) {
    transports.push(new winston_1.default.transports.Console({
        format: consoleFormat,
    }));
}
if (isProduction) {
    transports.push(new winston_1.default.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        format: fileFormat,
    }), new winston_1.default.transports.File({
        filename: 'logs/combined.log',
        format: fileFormat,
    }));
}
exports.logger = winston_1.default.createLogger({
    level: logLevel,
    format: fileFormat,
    defaultMeta: { service: 'ichat-backend' },
    transports,
    exitOnError: false,
});
if (isProduction) {
    exports.logger.exceptions.handle(new winston_1.default.transports.File({ filename: 'logs/exceptions.log' }));
    exports.logger.rejections.handle(new winston_1.default.transports.File({ filename: 'logs/rejections.log' }));
}
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map