"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = void 0;
exports.connectDatabase = connectDatabase;
exports.disconnectDatabase = disconnectDatabase;
const client_1 = require("@prisma/client");
const logger_1 = require("./logger");
const createPrismaClient = () => new client_1.PrismaClient({
    log: [
        {
            emit: 'event',
            level: 'query',
        },
        {
            emit: 'event',
            level: 'error',
        },
        {
            emit: 'event',
            level: 'info',
        },
        {
            emit: 'event',
            level: 'warn',
        },
    ],
});
const prisma = globalThis._prisma ?? createPrismaClient();
exports.prisma = prisma;
if (process.env.NODE_ENV === 'development') {
    globalThis._prisma = prisma;
}
prisma.$on('query', e => {
    if (process.env.LOG_LEVEL === 'debug') {
        logger_1.logger.debug('Query:', {
            query: e.query,
            params: e.params,
            duration: `${e.duration}ms`,
        });
    }
});
prisma.$on('error', e => {
    logger_1.logger.error('Prisma error:', e);
});
prisma.$on('info', e => {
    logger_1.logger.info('Prisma info:', e.message);
});
prisma.$on('warn', e => {
    logger_1.logger.warn('Prisma warning:', e.message);
});
async function connectDatabase() {
    try {
        await prisma.$connect();
        logger_1.logger.info('Successfully connected to database');
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to database:', error);
        throw error;
    }
}
async function disconnectDatabase() {
    try {
        await prisma.$disconnect();
        logger_1.logger.info('Successfully disconnected from database');
    }
    catch (error) {
        logger_1.logger.error('Failed to disconnect from database:', error);
        throw error;
    }
}
exports.default = prisma;
//# sourceMappingURL=database.js.map