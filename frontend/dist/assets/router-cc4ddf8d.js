import{r as s,R as se}from"./vendor-194d1c16.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)}var P;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(P||(P={}));const z="popstate";function ue(e){e===void 0&&(e={});function t(r,a){let{pathname:i,search:l,hash:u}=r.location;return $("",{pathname:i,search:l,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:N(a)}return fe(t,n,null,e)}function g(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Q(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ce(){return Math.random().toString(36).substr(2,8)}function D(e,t){return{usr:e.state,key:e.key,idx:t}}function $(e,t,n,r){return n===void 0&&(n=null),B({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?O(t):t,{state:n,key:t&&t.key||r||ce()})}function N(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function O(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function fe(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:i=!1}=r,l=a.history,u=P.Pop,o=null,f=d();f==null&&(f=0,l.replaceState(B({},l.state,{idx:f}),""));function d(){return(l.state||{idx:null}).idx}function c(){u=P.Pop;let h=d(),x=h==null?null:h-f;f=h,o&&o({action:u,location:m.location,delta:x})}function p(h,x){u=P.Push;let E=$(m.location,h,x);n&&n(E,h),f=d()+1;let C=D(E,f),R=m.createHref(E);try{l.pushState(C,"",R)}catch(U){if(U instanceof DOMException&&U.name==="DataCloneError")throw U;a.location.assign(R)}i&&o&&o({action:u,location:m.location,delta:1})}function y(h,x){u=P.Replace;let E=$(m.location,h,x);n&&n(E,h),f=d();let C=D(E,f),R=m.createHref(E);l.replaceState(C,"",R),i&&o&&o({action:u,location:m.location,delta:0})}function v(h){let x=a.location.origin!=="null"?a.location.origin:a.location.href,E=typeof h=="string"?h:N(h);return E=E.replace(/ $/,"%20"),g(x,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,x)}let m={get action(){return u},get location(){return e(a,l)},listen(h){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(z,c),o=h,()=>{a.removeEventListener(z,c),o=null}},createHref(h){return t(a,h)},createURL:v,encodeLocation(h){let x=v(h);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:y,go(h){return l.go(h)}};return m}var A;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(A||(A={}));function de(e,t,n){return n===void 0&&(n="/"),he(e,t,n,!1)}function he(e,t,n,r){let a=typeof t=="string"?O(t):t,i=F(a.pathname||"/",n);if(i==null)return null;let l=Y(e);pe(l);let u=null;for(let o=0;u==null&&o<l.length;++o){let f=be(i);u=Pe(l[o],f,r)}return u}function Y(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(i,l,u)=>{let o={relativePath:u===void 0?i.path||"":u,caseSensitive:i.caseSensitive===!0,childrenIndex:l,route:i};o.relativePath.startsWith("/")&&(g(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let f=w([r,o.relativePath]),d=n.concat(o);i.children&&i.children.length>0&&(g(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),Y(i.children,t,d,f)),!(i.path==null&&!i.index)&&t.push({path:f,score:Ee(f,i.index),routesMeta:d})};return e.forEach((i,l)=>{var u;if(i.path===""||!((u=i.path)!=null&&u.includes("?")))a(i,l);else for(let o of Z(i.path))a(i,l,o)}),t}function Z(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return a?[i,""]:[i];let l=Z(r.join("/")),u=[];return u.push(...l.map(o=>o===""?i:[i,o].join("/"))),a&&u.push(...l),u.map(o=>e.startsWith("/")&&o===""?"/":o)}function pe(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Re(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const me=/^:[\w-]+$/,ve=3,ge=2,ye=1,xe=10,Ce=-2,J=e=>e==="*";function Ee(e,t){let n=e.split("/"),r=n.length;return n.some(J)&&(r+=Ce),t&&(r+=ge),n.filter(a=>!J(a)).reduce((a,i)=>a+(me.test(i)?ve:i===""?ye:xe),r)}function Re(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function Pe(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,a={},i="/",l=[];for(let u=0;u<r.length;++u){let o=r[u],f=u===r.length-1,d=i==="/"?t:t.slice(i.length)||"/",c=K({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},d),p=o.route;if(!c&&f&&n&&!r[r.length-1].route.index&&(c=K({path:o.relativePath,caseSensitive:o.caseSensitive,end:!1},d)),!c)return null;Object.assign(a,c.params),l.push({params:a,pathname:w([i,c.pathname]),pathnameBase:Be(w([i,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(i=w([i,c.pathnameBase]))}return l}function K(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=we(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let i=a[0],l=i.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:r.reduce((f,d,c)=>{let{paramName:p,isOptional:y}=d;if(p==="*"){let m=u[c]||"";l=i.slice(0,i.length-m.length).replace(/(.)\/+$/,"$1")}const v=u[c];return y&&!v?f[p]=void 0:f[p]=(v||"").replace(/%2F/g,"/"),f},{}),pathname:i,pathnameBase:l,pattern:e}}function we(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Q(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,u,o)=>(r.push({paramName:u,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function be(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Q(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function F(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Se(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?O(e):e;return{pathname:n?n.startsWith("/")?n:Ue(n,t):t,search:Le(r),hash:Ie(a)}}function Ue(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function j(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Oe(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function ee(e,t){let n=Oe(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function te(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=O(e):(a=B({},e),g(!a.pathname||!a.pathname.includes("?"),j("?","pathname","search",a)),g(!a.pathname||!a.pathname.includes("#"),j("#","pathname","hash",a)),g(!a.search||!a.search.includes("#"),j("#","search","hash",a)));let i=e===""||a.pathname==="",l=i?"/":a.pathname,u;if(l==null)u=n;else{let c=t.length-1;if(!r&&l.startsWith("..")){let p=l.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}u=c>=0?t[c]:"/"}let o=Se(a,u),f=l&&l!=="/"&&l.endsWith("/"),d=(i||l===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(f||d)&&(o.pathname+="/"),o}const w=e=>e.join("/").replace(/\/\/+/g,"/"),Be=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Le=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ie=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ne(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const ne=["post","put","patch","delete"];new Set(ne);const Te=["get",...ne];new Set(Te);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function L(){return L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},L.apply(this,arguments)}const V=s.createContext(null),_e=s.createContext(null),S=s.createContext(null),_=s.createContext(null),b=s.createContext({outlet:null,matches:[],isDataRoute:!1}),re=s.createContext(null);function ke(e,t){let{relative:n}=t===void 0?{}:t;I()||g(!1);let{basename:r,navigator:a}=s.useContext(S),{hash:i,pathname:l,search:u}=ie(e,{relative:n}),o=l;return r!=="/"&&(o=l==="/"?r:w([r,l])),a.createHref({pathname:o,search:u,hash:i})}function I(){return s.useContext(_)!=null}function k(){return I()||g(!1),s.useContext(_).location}function ae(e){s.useContext(S).static||s.useLayoutEffect(e)}function je(){let{isDataRoute:e}=s.useContext(b);return e?Qe():$e()}function $e(){I()||g(!1);let e=s.useContext(V),{basename:t,future:n,navigator:r}=s.useContext(S),{matches:a}=s.useContext(b),{pathname:i}=k(),l=JSON.stringify(ee(a,n.v7_relativeSplatPath)),u=s.useRef(!1);return ae(()=>{u.current=!0}),s.useCallback(function(f,d){if(d===void 0&&(d={}),!u.current)return;if(typeof f=="number"){r.go(f);return}let c=te(f,JSON.parse(l),i,d.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:w([t,c.pathname])),(d.replace?r.replace:r.push)(c,d.state,d)},[t,r,l,i,e])}const We=s.createContext(null);function Me(e){let t=s.useContext(b).outlet;return t&&s.createElement(We.Provider,{value:e},t)}function ie(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=s.useContext(S),{matches:a}=s.useContext(b),{pathname:i}=k(),l=JSON.stringify(ee(a,r.v7_relativeSplatPath));return s.useMemo(()=>te(e,JSON.parse(l),i,n==="path"),[e,l,i,n])}function Fe(e,t){return Ve(e,t)}function Ve(e,t,n,r){I()||g(!1);let{navigator:a}=s.useContext(S),{matches:i}=s.useContext(b),l=i[i.length-1],u=l?l.params:{};l&&l.pathname;let o=l?l.pathnameBase:"/";l&&l.route;let f=k(),d;if(t){var c;let h=typeof t=="string"?O(t):t;o==="/"||(c=h.pathname)!=null&&c.startsWith(o)||g(!1),d=h}else d=f;let p=d.pathname||"/",y=p;if(o!=="/"){let h=o.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(h.length).join("/")}let v=de(e,{pathname:y}),m=Ke(v&&v.map(h=>Object.assign({},h,{params:Object.assign({},u,h.params),pathname:w([o,a.encodeLocation?a.encodeLocation(h.pathname).pathname:h.pathname]),pathnameBase:h.pathnameBase==="/"?o:w([o,a.encodeLocation?a.encodeLocation(h.pathnameBase).pathname:h.pathnameBase])})),i,n,r);return t&&m?s.createElement(_.Provider,{value:{location:L({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:P.Pop}},m):m}function ze(){let e=Xe(),t=Ne(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},i=null;return s.createElement(s.Fragment,null,s.createElement("h2",null,"Unexpected Application Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),n?s.createElement("pre",{style:a},n):null,i)}const De=s.createElement(ze,null);class Ae extends s.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?s.createElement(b.Provider,{value:this.props.routeContext},s.createElement(re.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Je(e){let{routeContext:t,match:n,children:r}=e,a=s.useContext(V);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),s.createElement(b.Provider,{value:t},r)}function Ke(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let l=e,u=(a=n)==null?void 0:a.errors;if(u!=null){let d=l.findIndex(c=>c.route.id&&(u==null?void 0:u[c.route.id])!==void 0);d>=0||g(!1),l=l.slice(0,Math.min(l.length,d+1))}let o=!1,f=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<l.length;d++){let c=l[d];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(f=d),c.route.id){let{loaderData:p,errors:y}=n,v=c.route.loader&&p[c.route.id]===void 0&&(!y||y[c.route.id]===void 0);if(c.route.lazy||v){o=!0,f>=0?l=l.slice(0,f+1):l=[l[0]];break}}}return l.reduceRight((d,c,p)=>{let y,v=!1,m=null,h=null;n&&(y=u&&c.route.id?u[c.route.id]:void 0,m=c.route.errorElement||De,o&&(f<0&&p===0?(Ye("route-fallback",!1),v=!0,h=null):f===p&&(v=!0,h=c.route.hydrateFallbackElement||null)));let x=t.concat(l.slice(0,p+1)),E=()=>{let C;return y?C=m:v?C=h:c.route.Component?C=s.createElement(c.route.Component,null):c.route.element?C=c.route.element:C=d,s.createElement(Je,{match:c,routeContext:{outlet:d,matches:x,isDataRoute:n!=null},children:C})};return n&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?s.createElement(Ae,{location:n.location,revalidation:n.revalidation,component:m,error:y,children:E(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):E()},null)}var le=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(le||{}),T=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(T||{});function qe(e){let t=s.useContext(V);return t||g(!1),t}function Ge(e){let t=s.useContext(_e);return t||g(!1),t}function He(e){let t=s.useContext(b);return t||g(!1),t}function oe(e){let t=He(),n=t.matches[t.matches.length-1];return n.route.id||g(!1),n.route.id}function Xe(){var e;let t=s.useContext(re),n=Ge(T.UseRouteError),r=oe(T.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Qe(){let{router:e}=qe(le.UseNavigateStable),t=oe(T.UseNavigateStable),n=s.useRef(!1);return ae(()=>{n.current=!0}),s.useCallback(function(a,i){i===void 0&&(i={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,L({fromRouteId:t},i)))},[e,t])}const q={};function Ye(e,t,n){!t&&!q[e]&&(q[e]=!0)}function Ze(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function dt(e){return Me(e.context)}function et(e){g(!1)}function tt(e){let{basename:t="/",children:n=null,location:r,navigationType:a=P.Pop,navigator:i,static:l=!1,future:u}=e;I()&&g(!1);let o=t.replace(/^\/*/,"/"),f=s.useMemo(()=>({basename:o,navigator:i,static:l,future:L({v7_relativeSplatPath:!1},u)}),[o,u,i,l]);typeof r=="string"&&(r=O(r));let{pathname:d="/",search:c="",hash:p="",state:y=null,key:v="default"}=r,m=s.useMemo(()=>{let h=F(d,o);return h==null?null:{location:{pathname:h,search:c,hash:p,state:y,key:v},navigationType:a}},[o,d,c,p,y,v,a]);return m==null?null:s.createElement(S.Provider,{value:f},s.createElement(_.Provider,{children:n,value:m}))}function ht(e){let{children:t,location:n}=e;return Fe(W(t),n)}new Promise(()=>{});function W(e,t){t===void 0&&(t=[]);let n=[];return s.Children.forEach(e,(r,a)=>{if(!s.isValidElement(r))return;let i=[...t,a];if(r.type===s.Fragment){n.push.apply(n,W(r.props.children,i));return}r.type!==et&&g(!1),!r.props.index||!r.props.children||g(!1);let l={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(l.children=W(r.props.children,i)),n.push(l)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},M.apply(this,arguments)}function nt(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,i;for(i=0;i<r.length;i++)a=r[i],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function rt(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function at(e,t){return e.button===0&&(!t||t==="_self")&&!rt(e)}const it=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],lt="6";try{window.__reactRouterVersion=lt}catch{}const ot="startTransition",G=se[ot];function pt(e){let{basename:t,children:n,future:r,window:a}=e,i=s.useRef();i.current==null&&(i.current=ue({window:a,v5Compat:!0}));let l=i.current,[u,o]=s.useState({action:l.action,location:l.location}),{v7_startTransition:f}=r||{},d=s.useCallback(c=>{f&&G?G(()=>o(c)):o(c)},[o,f]);return s.useLayoutEffect(()=>l.listen(d),[l,d]),s.useEffect(()=>Ze(r),[r]),s.createElement(tt,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:l,future:r})}const st=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ut=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,mt=s.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:i,replace:l,state:u,target:o,to:f,preventScrollReset:d,viewTransition:c}=t,p=nt(t,it),{basename:y}=s.useContext(S),v,m=!1;if(typeof f=="string"&&ut.test(f)&&(v=f,st))try{let C=new URL(window.location.href),R=f.startsWith("//")?new URL(C.protocol+f):new URL(f),U=F(R.pathname,y);R.origin===C.origin&&U!=null?f=U+R.search+R.hash:m=!0}catch{}let h=ke(f,{relative:a}),x=ct(f,{replace:l,state:u,target:o,preventScrollReset:d,relative:a,viewTransition:c});function E(C){r&&r(C),C.defaultPrevented||x(C)}return s.createElement("a",M({},p,{href:v||h,onClick:m||i?r:E,ref:n,target:o}))});var H;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(H||(H={}));var X;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(X||(X={}));function ct(e,t){let{target:n,replace:r,state:a,preventScrollReset:i,relative:l,viewTransition:u}=t===void 0?{}:t,o=je(),f=k(),d=ie(e,{relative:l});return s.useCallback(c=>{if(at(c,n)){c.preventDefault();let p=r!==void 0?r:N(f)===N(d);o(e,{replace:p,state:a,preventScrollReset:i,relative:l,viewTransition:u})}},[f,o,d,r,a,n,e,i,l,u])}export{pt as B,mt as L,dt as O,ht as R,et as a,k as u};
//# sourceMappingURL=router-cc4ddf8d.js.map
