import{r as p,a as Ze,b as Ye,c as U}from"./vendor-194d1c16.js";import{u as Je,L as Xe,O as et,R as tt,a as k,B as nt}from"./router-cc4ddf8d.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))e(r);new MutationObserver(r=>{for(const s of r)if(s.type==="childList")for(const a of s.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&e(a)}).observe(document,{childList:!0,subtree:!0});function i(r){const s={};return r.integrity&&(s.integrity=r.integrity),r.referrerPolicy&&(s.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?s.credentials="include":r.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function e(r){if(r.ep)return;r.ep=!0;const s=i(r);fetch(r.href,s)}})();var Ee={exports:{}},te={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rt=p,it=Symbol.for("react.element"),st=Symbol.for("react.fragment"),at=Object.prototype.hasOwnProperty,ot=rt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ut={key:!0,ref:!0,__self:!0,__source:!0};function _e(n,t,i){var e,r={},s=null,a=null;i!==void 0&&(s=""+i),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(a=t.ref);for(e in t)at.call(t,e)&&!ut.hasOwnProperty(e)&&(r[e]=t[e]);if(n&&n.defaultProps)for(e in t=n.defaultProps,t)r[e]===void 0&&(r[e]=t[e]);return{$$typeof:it,type:n,key:s,ref:a,props:r,_owner:ot.current}}te.Fragment=st;te.jsx=_e;te.jsxs=_e;Ee.exports=te;var u=Ee.exports,le={},be=Ze;le.createRoot=be.createRoot,le.hydrateRoot=be.hydrateRoot;let ct={data:""},lt=n=>typeof window=="object"?((n?n.querySelector("#_goober"):window._goober)||Object.assign((n||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:n||ct,dt=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,ft=/\/\*[^]*?\*\/|  +/g,je=/\n+/g,$=(n,t)=>{let i="",e="",r="";for(let s in n){let a=n[s];s[0]=="@"?s[1]=="i"?i=s+" "+a+";":e+=s[1]=="f"?$(a,s):s+"{"+$(a,s[1]=="k"?"":t)+"}":typeof a=="object"?e+=$(a,t?t.replace(/([^,])+/g,o=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,c=>/&/.test(c)?c.replace(/&/g,o):o?o+" "+c:c)):s):a!=null&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=$.p?$.p(s,a):s+":"+a+";")}return i+(t&&r?t+"{"+r+"}":r)+e},_={},Ae=n=>{if(typeof n=="object"){let t="";for(let i in n)t+=i+Ae(n[i]);return t}return n},ht=(n,t,i,e,r)=>{let s=Ae(n),a=_[s]||(_[s]=(c=>{let l=0,d=11;for(;l<c.length;)d=101*d+c.charCodeAt(l++)>>>0;return"go"+d})(s));if(!_[a]){let c=s!==n?n:(l=>{let d,h,m=[{}];for(;d=dt.exec(l.replace(ft,""));)d[4]?m.shift():d[3]?(h=d[3].replace(je," ").trim(),m.unshift(m[0][h]=m[0][h]||{})):m[0][d[1]]=d[2].replace(je," ").trim();return m[0]})(n);_[a]=$(r?{["@keyframes "+a]:c}:c,i?"":"."+a)}let o=i&&_.g?_.g:null;return i&&(_.g=_[a]),((c,l,d,h)=>{h?l.data=l.data.replace(h,c):l.data.indexOf(c)===-1&&(l.data=d?c+l.data:l.data+c)})(_[a],t,e,o),a},pt=(n,t,i)=>n.reduce((e,r,s)=>{let a=t[s];if(a&&a.call){let o=a(i),c=o&&o.props&&o.props.className||/^go/.test(o)&&o;a=c?"."+c:o&&typeof o=="object"?o.props?"":$(o,""):o===!1?"":o}return e+r+(a??"")},"");function ne(n){let t=this||{},i=n.call?n(t.p):n;return ht(i.unshift?i.raw?pt(i,[].slice.call(arguments,1),t.p):i.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):i,lt(t.target),t.g,t.o,t.k)}let De,de,fe;ne.bind({g:1});let A=ne.bind({k:1});function mt(n,t,i,e){$.p=t,De=n,de=i,fe=e}function Q(n,t){let i=this||{};return function(){let e=arguments;function r(s,a){let o=Object.assign({},s),c=o.className||r.className;i.p=Object.assign({theme:de&&de()},o),i.o=/ *go\d+/.test(c),o.className=ne.apply(i,e)+(c?" "+c:""),t&&(o.ref=a);let l=n;return n[0]&&(l=o.as||n,delete o.as),fe&&l[0]&&fe(o),De(l,o)}return t?t(r):r}}var vt=n=>typeof n=="function",Y=(n,t)=>vt(n)?n(t):n,yt=(()=>{let n=0;return()=>(++n).toString()})(),Me=(()=>{let n;return()=>{if(n===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");n=!t||t.matches}return n}})(),gt=20,me="default",Se=(n,t)=>{let{toastLimit:i}=n.settings;switch(t.type){case 0:return{...n,toasts:[t.toast,...n.toasts].slice(0,i)};case 1:return{...n,toasts:n.toasts.map(a=>a.id===t.toast.id?{...a,...t.toast}:a)};case 2:let{toast:e}=t;return Se(n,{type:n.toasts.find(a=>a.id===e.id)?1:0,toast:e});case 3:let{toastId:r}=t;return{...n,toasts:n.toasts.map(a=>a.id===r||r===void 0?{...a,dismissed:!0,visible:!1}:a)};case 4:return t.toastId===void 0?{...n,toasts:[]}:{...n,toasts:n.toasts.filter(a=>a.id!==t.toastId)};case 5:return{...n,pausedAt:t.time};case 6:let s=t.time-(n.pausedAt||0);return{...n,pausedAt:void 0,toasts:n.toasts.map(a=>({...a,pauseDuration:a.pauseDuration+s}))}}},z=[],$e={toasts:[],pausedAt:void 0,settings:{toastLimit:gt}},N={},Qe=(n,t=me)=>{N[t]=Se(N[t]||$e,n),z.forEach(([i,e])=>{i===t&&e(N[t])})},Re=n=>Object.keys(N).forEach(t=>Qe(n,t)),xt=n=>Object.keys(N).find(t=>N[t].toasts.some(i=>i.id===n)),re=(n=me)=>t=>{Qe(t,n)},bt={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},jt=(n={},t=me)=>{let[i,e]=p.useState(N[t]||$e),r=p.useRef(N[t]);p.useEffect(()=>(r.current!==N[t]&&e(N[t]),z.push([t,e]),()=>{let a=z.findIndex(([o])=>o===t);a>-1&&z.splice(a,1)}),[t]);let s=i.toasts.map(a=>{var o,c,l;return{...n,...n[a.type],...a,removeDelay:a.removeDelay||((o=n[a.type])==null?void 0:o.removeDelay)||(n==null?void 0:n.removeDelay),duration:a.duration||((c=n[a.type])==null?void 0:c.duration)||(n==null?void 0:n.duration)||bt[a.type],style:{...n.style,...(l=n[a.type])==null?void 0:l.style,...a.style}}});return{...i,toasts:s}},wt=(n,t="blank",i)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:n,pauseDuration:0,...i,id:(i==null?void 0:i.id)||yt()}),B=n=>(t,i)=>{let e=wt(t,n,i);return re(e.toasterId||xt(e.id))({type:2,toast:e}),e.id},j=(n,t)=>B("blank")(n,t);j.error=B("error");j.success=B("success");j.loading=B("loading");j.custom=B("custom");j.dismiss=(n,t)=>{let i={type:3,toastId:n};t?re(t)(i):Re(i)};j.dismissAll=n=>j.dismiss(void 0,n);j.remove=(n,t)=>{let i={type:4,toastId:n};t?re(t)(i):Re(i)};j.removeAll=n=>j.remove(void 0,n);j.promise=(n,t,i)=>{let e=j.loading(t.loading,{...i,...i==null?void 0:i.loading});return typeof n=="function"&&(n=n()),n.then(r=>{let s=t.success?Y(t.success,r):void 0;return s?j.success(s,{id:e,...i,...i==null?void 0:i.success}):j.dismiss(e),r}).catch(r=>{let s=t.error?Y(t.error,r):void 0;s?j.error(s,{id:e,...i,...i==null?void 0:i.error}):j.dismiss(e)}),n};var Ot=1e3,Ct=(n,t="default")=>{let{toasts:i,pausedAt:e}=jt(n,t),r=p.useRef(new Map).current,s=p.useCallback((h,m=Ot)=>{if(r.has(h))return;let f=setTimeout(()=>{r.delete(h),a({type:4,toastId:h})},m);r.set(h,f)},[]);p.useEffect(()=>{if(e)return;let h=Date.now(),m=i.map(f=>{if(f.duration===1/0)return;let g=(f.duration||0)+f.pauseDuration-(h-f.createdAt);if(g<0){f.visible&&j.dismiss(f.id);return}return setTimeout(()=>j.dismiss(f.id,t),g)});return()=>{m.forEach(f=>f&&clearTimeout(f))}},[i,e,t]);let a=p.useCallback(re(t),[t]),o=p.useCallback(()=>{a({type:5,time:Date.now()})},[a]),c=p.useCallback((h,m)=>{a({type:1,toast:{id:h,height:m}})},[a]),l=p.useCallback(()=>{e&&a({type:6,time:Date.now()})},[e,a]),d=p.useCallback((h,m)=>{let{reverseOrder:f=!1,gutter:g=8,defaultPosition:x}=m||{},P=i.filter(y=>(y.position||x)===(h.position||x)&&y.height),O=P.findIndex(y=>y.id===h.id),b=P.filter((y,q)=>q<O&&y.visible).length;return P.filter(y=>y.visible).slice(...f?[b+1]:[0,b]).reduce((y,q)=>y+(q.height||0)+g,0)},[i]);return p.useEffect(()=>{i.forEach(h=>{if(h.dismissed)s(h.id,h.removeDelay);else{let m=r.get(h.id);m&&(clearTimeout(m),r.delete(h.id))}})},[i,s]),{toasts:i,handlers:{updateHeight:c,startPause:o,endPause:l,calculateOffset:d}}},Pt=A`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Ft=A`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Nt=A`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,qt=Q("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${n=>n.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Pt} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Ft} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${n=>n.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Nt} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Et=A`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,_t=Q("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${n=>n.secondary||"#e0e0e0"};
  border-right-color: ${n=>n.primary||"#616161"};
  animation: ${Et} 1s linear infinite;
`,At=A`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Dt=A`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Mt=Q("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${n=>n.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${At} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Dt} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${n=>n.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,St=Q("div")`
  position: absolute;
`,$t=Q("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Qt=A`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Rt=Q("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Qt} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Lt=({toast:n})=>{let{icon:t,type:i,iconTheme:e}=n;return t!==void 0?typeof t=="string"?p.createElement(Rt,null,t):t:i==="blank"?null:p.createElement($t,null,p.createElement(_t,{...e}),i!=="loading"&&p.createElement(St,null,i==="error"?p.createElement(qt,{...e}):p.createElement(Mt,{...e})))},kt=n=>`
0% {transform: translate3d(0,${n*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,It=n=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${n*-150}%,-1px) scale(.6); opacity:0;}
`,Tt="0%{opacity:0;} 100%{opacity:1;}",Ut="0%{opacity:1;} 100%{opacity:0;}",Kt=Q("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Ht=Q("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Bt=(n,t)=>{let i=n.includes("top")?1:-1,[e,r]=Me()?[Tt,Ut]:[kt(i),It(i)];return{animation:t?`${A(e)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${A(r)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},Wt=p.memo(({toast:n,position:t,style:i,children:e})=>{let r=n.height?Bt(n.position||t||"top-center",n.visible):{opacity:0},s=p.createElement(Lt,{toast:n}),a=p.createElement(Ht,{...n.ariaProps},Y(n.message,n));return p.createElement(Kt,{className:n.className,style:{...r,...i,...n.style}},typeof e=="function"?e({icon:s,message:a}):p.createElement(p.Fragment,null,s,a))});mt(p.createElement);var Gt=({id:n,className:t,style:i,onHeightUpdate:e,children:r})=>{let s=p.useCallback(a=>{if(a){let o=()=>{let c=a.getBoundingClientRect().height;e(n,c)};o(),new MutationObserver(o).observe(a,{subtree:!0,childList:!0,characterData:!0})}},[n,e]);return p.createElement("div",{ref:s,className:t,style:i},r)},zt=(n,t)=>{let i=n.includes("top"),e=i?{top:0}:{bottom:0},r=n.includes("center")?{justifyContent:"center"}:n.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:Me()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(i?1:-1)}px)`,...e,...r}},Vt=ne`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,G=16,Zt=({reverseOrder:n,position:t="top-center",toastOptions:i,gutter:e,children:r,toasterId:s,containerStyle:a,containerClassName:o})=>{let{toasts:c,handlers:l}=Ct(i,s);return p.createElement("div",{"data-rht-toaster":s||"",style:{position:"fixed",zIndex:9999,top:G,left:G,right:G,bottom:G,pointerEvents:"none",...a},className:o,onMouseEnter:l.startPause,onMouseLeave:l.endPause},c.map(d=>{let h=d.position||t,m=l.calculateOffset(d,{reverseOrder:n,gutter:e,defaultPosition:t}),f=zt(h,m);return p.createElement(Gt,{id:d.id,key:d.id,onHeightUpdate:l.updateHeight,className:d.visible?Vt:"",style:f},d.type==="custom"?Y(d.message,d):r?r(d):p.createElement(Wt,{toast:d,position:h}))}))};function he(n,t){return he=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(i,e){return i.__proto__=e,i},he(n,t)}function ie(n,t){n.prototype=Object.create(t.prototype),n.prototype.constructor=n,he(n,t)}var se=function(){function n(){this.listeners=[]}var t=n.prototype;return t.subscribe=function(e){var r=this,s=e||function(){};return this.listeners.push(s),this.onSubscribe(),function(){r.listeners=r.listeners.filter(function(a){return a!==s}),r.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},n}();function v(){return v=Object.assign?Object.assign.bind():function(n){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var e in i)({}).hasOwnProperty.call(i,e)&&(n[e]=i[e])}return n},v.apply(null,arguments)}var Le=typeof window>"u";function C(){}function Yt(n,t){return typeof n=="function"?n(t):n}function Jt(n){return typeof n=="number"&&n>=0&&n!==1/0}function J(n){return Array.isArray(n)?n:[n]}function Xt(n,t){return Math.max(n+(t||0)-Date.now(),0)}function ue(n,t,i){return ae(n)?typeof t=="function"?v({},i,{queryKey:n,queryFn:t}):v({},t,{queryKey:n}):n}function S(n,t,i){return ae(n)?[v({},t,{queryKey:n}),i]:[n||{},t]}function en(n,t){if(n===!0&&t===!0||n==null&&t==null)return"all";if(n===!1&&t===!1)return"none";var i=n??!t;return i?"active":"inactive"}function we(n,t){var i=n.active,e=n.exact,r=n.fetching,s=n.inactive,a=n.predicate,o=n.queryKey,c=n.stale;if(ae(o)){if(e){if(t.queryHash!==ve(o,t.options))return!1}else if(!X(t.queryKey,o))return!1}var l=en(i,s);if(l==="none")return!1;if(l!=="all"){var d=t.isActive();if(l==="active"&&!d||l==="inactive"&&d)return!1}return!(typeof c=="boolean"&&t.isStale()!==c||typeof r=="boolean"&&t.isFetching()!==r||a&&!a(t))}function Oe(n,t){var i=n.exact,e=n.fetching,r=n.predicate,s=n.mutationKey;if(ae(s)){if(!t.options.mutationKey)return!1;if(i){if(I(t.options.mutationKey)!==I(s))return!1}else if(!X(t.options.mutationKey,s))return!1}return!(typeof e=="boolean"&&t.state.status==="loading"!==e||r&&!r(t))}function ve(n,t){var i=(t==null?void 0:t.queryKeyHashFn)||I;return i(n)}function I(n){var t=J(n);return tn(t)}function tn(n){return JSON.stringify(n,function(t,i){return pe(i)?Object.keys(i).sort().reduce(function(e,r){return e[r]=i[r],e},{}):i})}function X(n,t){return ke(J(n),J(t))}function ke(n,t){return n===t?!0:typeof n!=typeof t?!1:n&&t&&typeof n=="object"&&typeof t=="object"?!Object.keys(t).some(function(i){return!ke(n[i],t[i])}):!1}function Ie(n,t){if(n===t)return n;var i=Array.isArray(n)&&Array.isArray(t);if(i||pe(n)&&pe(t)){for(var e=i?n.length:Object.keys(n).length,r=i?t:Object.keys(t),s=r.length,a=i?[]:{},o=0,c=0;c<s;c++){var l=i?c:r[c];a[l]=Ie(n[l],t[l]),a[l]===n[l]&&o++}return e===s&&o===e?n:a}return t}function pe(n){if(!Ce(n))return!1;var t=n.constructor;if(typeof t>"u")return!0;var i=t.prototype;return!(!Ce(i)||!i.hasOwnProperty("isPrototypeOf"))}function Ce(n){return Object.prototype.toString.call(n)==="[object Object]"}function ae(n){return typeof n=="string"||Array.isArray(n)}function nn(n){return new Promise(function(t){setTimeout(t,n)})}function Pe(n){Promise.resolve().then(n).catch(function(t){return setTimeout(function(){throw t})})}function Te(){if(typeof AbortController=="function")return new AbortController}var rn=function(n){ie(t,n);function t(){var e;return e=n.call(this)||this,e.setup=function(r){var s;if(!Le&&((s=window)!=null&&s.addEventListener)){var a=function(){return r()};return window.addEventListener("visibilitychange",a,!1),window.addEventListener("focus",a,!1),function(){window.removeEventListener("visibilitychange",a),window.removeEventListener("focus",a)}}},e}var i=t.prototype;return i.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},i.onUnsubscribe=function(){if(!this.hasListeners()){var r;(r=this.cleanup)==null||r.call(this),this.cleanup=void 0}},i.setEventListener=function(r){var s,a=this;this.setup=r,(s=this.cleanup)==null||s.call(this),this.cleanup=r(function(o){typeof o=="boolean"?a.setFocused(o):a.onFocus()})},i.setFocused=function(r){this.focused=r,r&&this.onFocus()},i.onFocus=function(){this.listeners.forEach(function(r){r()})},i.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},t}(se),V=new rn,sn=function(n){ie(t,n);function t(){var e;return e=n.call(this)||this,e.setup=function(r){var s;if(!Le&&((s=window)!=null&&s.addEventListener)){var a=function(){return r()};return window.addEventListener("online",a,!1),window.addEventListener("offline",a,!1),function(){window.removeEventListener("online",a),window.removeEventListener("offline",a)}}},e}var i=t.prototype;return i.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},i.onUnsubscribe=function(){if(!this.hasListeners()){var r;(r=this.cleanup)==null||r.call(this),this.cleanup=void 0}},i.setEventListener=function(r){var s,a=this;this.setup=r,(s=this.cleanup)==null||s.call(this),this.cleanup=r(function(o){typeof o=="boolean"?a.setOnline(o):a.onOnline()})},i.setOnline=function(r){this.online=r,r&&this.onOnline()},i.onOnline=function(){this.listeners.forEach(function(r){r()})},i.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},t}(se),Z=new sn;function an(n){return Math.min(1e3*Math.pow(2,n),3e4)}function ee(n){return typeof(n==null?void 0:n.cancel)=="function"}var Ue=function(t){this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent};function ce(n){return n instanceof Ue}var Ke=function(t){var i=this,e=!1,r,s,a,o;this.abort=t.abort,this.cancel=function(m){return r==null?void 0:r(m)},this.cancelRetry=function(){e=!0},this.continueRetry=function(){e=!1},this.continue=function(){return s==null?void 0:s()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(m,f){a=m,o=f});var c=function(f){i.isResolved||(i.isResolved=!0,t.onSuccess==null||t.onSuccess(f),s==null||s(),a(f))},l=function(f){i.isResolved||(i.isResolved=!0,t.onError==null||t.onError(f),s==null||s(),o(f))},d=function(){return new Promise(function(f){s=f,i.isPaused=!0,t.onPause==null||t.onPause()}).then(function(){s=void 0,i.isPaused=!1,t.onContinue==null||t.onContinue()})},h=function m(){if(!i.isResolved){var f;try{f=t.fn()}catch(g){f=Promise.reject(g)}r=function(x){if(!i.isResolved&&(l(new Ue(x)),i.abort==null||i.abort(),ee(f)))try{f.cancel()}catch{}},i.isTransportCancelable=ee(f),Promise.resolve(f).then(c).catch(function(g){var x,P;if(!i.isResolved){var O=(x=t.retry)!=null?x:3,b=(P=t.retryDelay)!=null?P:an,y=typeof b=="function"?b(i.failureCount,g):b,q=O===!0||typeof O=="number"&&i.failureCount<O||typeof O=="function"&&O(i.failureCount,g);if(e||!q){l(g);return}i.failureCount++,t.onFail==null||t.onFail(i.failureCount,g),nn(y).then(function(){if(!V.isFocused()||!Z.isOnline())return d()}).then(function(){e?l(g):m()})}})}};h()},on=function(){function n(){this.queue=[],this.transactions=0,this.notifyFn=function(i){i()},this.batchNotifyFn=function(i){i()}}var t=n.prototype;return t.batch=function(e){var r;this.transactions++;try{r=e()}finally{this.transactions--,this.transactions||this.flush()}return r},t.schedule=function(e){var r=this;this.transactions?this.queue.push(e):Pe(function(){r.notifyFn(e)})},t.batchCalls=function(e){var r=this;return function(){for(var s=arguments.length,a=new Array(s),o=0;o<s;o++)a[o]=arguments[o];r.schedule(function(){e.apply(void 0,a)})}},t.flush=function(){var e=this,r=this.queue;this.queue=[],r.length&&Pe(function(){e.batchNotifyFn(function(){r.forEach(function(s){e.notifyFn(s)})})})},t.setNotifyFunction=function(e){this.notifyFn=e},t.setBatchNotifyFunction=function(e){this.batchNotifyFn=e},n}(),w=new on,He=console;function Be(){return He}function un(n){He=n}var cn=function(){function n(i){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=i.defaultOptions,this.setOptions(i.options),this.observers=[],this.cache=i.cache,this.queryKey=i.queryKey,this.queryHash=i.queryHash,this.initialState=i.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=i.meta,this.scheduleGc()}var t=n.prototype;return t.setOptions=function(e){var r;this.options=v({},this.defaultOptions,e),this.meta=e==null?void 0:e.meta,this.cacheTime=Math.max(this.cacheTime||0,(r=this.options.cacheTime)!=null?r:5*60*1e3)},t.setDefaultOptions=function(e){this.defaultOptions=e},t.scheduleGc=function(){var e=this;this.clearGcTimeout(),Jt(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){e.optionalRemove()},this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(e,r){var s,a,o=this.state.data,c=Yt(e,o);return(s=(a=this.options).isDataEqual)!=null&&s.call(a,o,c)?c=o:this.options.structuralSharing!==!1&&(c=Ie(o,c)),this.dispatch({data:c,type:"success",dataUpdatedAt:r==null?void 0:r.updatedAt}),c},t.setState=function(e,r){this.dispatch({type:"setState",state:e,setStateOptions:r})},t.cancel=function(e){var r,s=this.promise;return(r=this.retryer)==null||r.cancel(e),s?s.then(C).catch(C):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some(function(e){return e.options.enabled!==!1})},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(e){return e.getCurrentResult().isStale})},t.isStaleByTime=function(e){return e===void 0&&(e=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!Xt(this.state.dataUpdatedAt,e)},t.onFocus=function(){var e,r=this.observers.find(function(s){return s.shouldFetchOnWindowFocus()});r&&r.refetch(),(e=this.retryer)==null||e.continue()},t.onOnline=function(){var e,r=this.observers.find(function(s){return s.shouldFetchOnReconnect()});r&&r.refetch(),(e=this.retryer)==null||e.continue()},t.addObserver=function(e){this.observers.indexOf(e)===-1&&(this.observers.push(e),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:e}))},t.removeObserver=function(e){this.observers.indexOf(e)!==-1&&(this.observers=this.observers.filter(function(r){return r!==e}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:e}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(e,r){var s=this,a,o,c;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(r!=null&&r.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var l;return(l=this.retryer)==null||l.continueRetry(),this.promise}}if(e&&this.setOptions(e),!this.options.queryFn){var d=this.observers.find(function(b){return b.options.queryFn});d&&this.setOptions(d.options)}var h=J(this.queryKey),m=Te(),f={queryKey:h,pageParam:void 0,meta:this.meta};Object.defineProperty(f,"signal",{enumerable:!0,get:function(){if(m)return s.abortSignalConsumed=!0,m.signal}});var g=function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(f)):Promise.reject("Missing queryFn")},x={fetchOptions:r,options:this.options,queryKey:h,state:this.state,fetchFn:g,meta:this.meta};if((a=this.options.behavior)!=null&&a.onFetch){var P;(P=this.options.behavior)==null||P.onFetch(x)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((o=x.fetchOptions)==null?void 0:o.meta)){var O;this.dispatch({type:"fetch",meta:(O=x.fetchOptions)==null?void 0:O.meta})}return this.retryer=new Ke({fn:x.fetchFn,abort:m==null||(c=m.abort)==null?void 0:c.bind(m),onSuccess:function(y){s.setData(y),s.cache.config.onSuccess==null||s.cache.config.onSuccess(y,s),s.cacheTime===0&&s.optionalRemove()},onError:function(y){ce(y)&&y.silent||s.dispatch({type:"error",error:y}),ce(y)||(s.cache.config.onError==null||s.cache.config.onError(y,s),Be().error(y)),s.cacheTime===0&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:x.options.retry,retryDelay:x.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(e){var r=this;this.state=this.reducer(this.state,e),w.batch(function(){r.observers.forEach(function(s){s.onQueryUpdate(e)}),r.cache.notify({query:r,type:"queryUpdated",action:e})})},t.getDefaultState=function(e){var r=typeof e.initialData=="function"?e.initialData():e.initialData,s=typeof e.initialData<"u",a=s?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0,o=typeof r<"u";return{data:r,dataUpdateCount:0,dataUpdatedAt:o?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:o?"success":"idle"}},t.reducer=function(e,r){var s,a;switch(r.type){case"failed":return v({},e,{fetchFailureCount:e.fetchFailureCount+1});case"pause":return v({},e,{isPaused:!0});case"continue":return v({},e,{isPaused:!1});case"fetch":return v({},e,{fetchFailureCount:0,fetchMeta:(s=r.meta)!=null?s:null,isFetching:!0,isPaused:!1},!e.dataUpdatedAt&&{error:null,status:"loading"});case"success":return v({},e,{data:r.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:(a=r.dataUpdatedAt)!=null?a:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var o=r.error;return ce(o)&&o.revert&&this.revertState?v({},this.revertState):v({},e,{error:o,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return v({},e,{isInvalidated:!0});case"setState":return v({},e,r.state);default:return e}},n}(),ln=function(n){ie(t,n);function t(e){var r;return r=n.call(this)||this,r.config=e||{},r.queries=[],r.queriesMap={},r}var i=t.prototype;return i.build=function(r,s,a){var o,c=s.queryKey,l=(o=s.queryHash)!=null?o:ve(c,s),d=this.get(l);return d||(d=new cn({cache:this,queryKey:c,queryHash:l,options:r.defaultQueryOptions(s),state:a,defaultOptions:r.getQueryDefaults(c),meta:s.meta}),this.add(d)),d},i.add=function(r){this.queriesMap[r.queryHash]||(this.queriesMap[r.queryHash]=r,this.queries.push(r),this.notify({type:"queryAdded",query:r}))},i.remove=function(r){var s=this.queriesMap[r.queryHash];s&&(r.destroy(),this.queries=this.queries.filter(function(a){return a!==r}),s===r&&delete this.queriesMap[r.queryHash],this.notify({type:"queryRemoved",query:r}))},i.clear=function(){var r=this;w.batch(function(){r.queries.forEach(function(s){r.remove(s)})})},i.get=function(r){return this.queriesMap[r]},i.getAll=function(){return this.queries},i.find=function(r,s){var a=S(r,s),o=a[0];return typeof o.exact>"u"&&(o.exact=!0),this.queries.find(function(c){return we(o,c)})},i.findAll=function(r,s){var a=S(r,s),o=a[0];return Object.keys(o).length>0?this.queries.filter(function(c){return we(o,c)}):this.queries},i.notify=function(r){var s=this;w.batch(function(){s.listeners.forEach(function(a){a(r)})})},i.onFocus=function(){var r=this;w.batch(function(){r.queries.forEach(function(s){s.onFocus()})})},i.onOnline=function(){var r=this;w.batch(function(){r.queries.forEach(function(s){s.onOnline()})})},t}(se),dn=function(){function n(i){this.options=v({},i.defaultOptions,i.options),this.mutationId=i.mutationId,this.mutationCache=i.mutationCache,this.observers=[],this.state=i.state||fn(),this.meta=i.meta}var t=n.prototype;return t.setState=function(e){this.dispatch({type:"setState",state:e})},t.addObserver=function(e){this.observers.indexOf(e)===-1&&this.observers.push(e)},t.removeObserver=function(e){this.observers=this.observers.filter(function(r){return r!==e})},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(C).catch(C)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var e=this,r,s=this.state.status==="loading",a=Promise.resolve();return s||(this.dispatch({type:"loading",variables:this.options.variables}),a=a.then(function(){e.mutationCache.config.onMutate==null||e.mutationCache.config.onMutate(e.state.variables,e)}).then(function(){return e.options.onMutate==null?void 0:e.options.onMutate(e.state.variables)}).then(function(o){o!==e.state.context&&e.dispatch({type:"loading",context:o,variables:e.state.variables})})),a.then(function(){return e.executeMutation()}).then(function(o){r=o,e.mutationCache.config.onSuccess==null||e.mutationCache.config.onSuccess(r,e.state.variables,e.state.context,e)}).then(function(){return e.options.onSuccess==null?void 0:e.options.onSuccess(r,e.state.variables,e.state.context)}).then(function(){return e.options.onSettled==null?void 0:e.options.onSettled(r,null,e.state.variables,e.state.context)}).then(function(){return e.dispatch({type:"success",data:r}),r}).catch(function(o){return e.mutationCache.config.onError==null||e.mutationCache.config.onError(o,e.state.variables,e.state.context,e),Be().error(o),Promise.resolve().then(function(){return e.options.onError==null?void 0:e.options.onError(o,e.state.variables,e.state.context)}).then(function(){return e.options.onSettled==null?void 0:e.options.onSettled(void 0,o,e.state.variables,e.state.context)}).then(function(){throw e.dispatch({type:"error",error:o}),o})})},t.executeMutation=function(){var e=this,r;return this.retryer=new Ke({fn:function(){return e.options.mutationFn?e.options.mutationFn(e.state.variables):Promise.reject("No mutationFn found")},onFail:function(){e.dispatch({type:"failed"})},onPause:function(){e.dispatch({type:"pause"})},onContinue:function(){e.dispatch({type:"continue"})},retry:(r=this.options.retry)!=null?r:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(e){var r=this;this.state=hn(this.state,e),w.batch(function(){r.observers.forEach(function(s){s.onMutationUpdate(e)}),r.mutationCache.notify(r)})},n}();function fn(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function hn(n,t){switch(t.type){case"failed":return v({},n,{failureCount:n.failureCount+1});case"pause":return v({},n,{isPaused:!0});case"continue":return v({},n,{isPaused:!1});case"loading":return v({},n,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return v({},n,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return v({},n,{data:void 0,error:t.error,failureCount:n.failureCount+1,isPaused:!1,status:"error"});case"setState":return v({},n,t.state);default:return n}}var pn=function(n){ie(t,n);function t(e){var r;return r=n.call(this)||this,r.config=e||{},r.mutations=[],r.mutationId=0,r}var i=t.prototype;return i.build=function(r,s,a){var o=new dn({mutationCache:this,mutationId:++this.mutationId,options:r.defaultMutationOptions(s),state:a,defaultOptions:s.mutationKey?r.getMutationDefaults(s.mutationKey):void 0,meta:s.meta});return this.add(o),o},i.add=function(r){this.mutations.push(r),this.notify(r)},i.remove=function(r){this.mutations=this.mutations.filter(function(s){return s!==r}),r.cancel(),this.notify(r)},i.clear=function(){var r=this;w.batch(function(){r.mutations.forEach(function(s){r.remove(s)})})},i.getAll=function(){return this.mutations},i.find=function(r){return typeof r.exact>"u"&&(r.exact=!0),this.mutations.find(function(s){return Oe(r,s)})},i.findAll=function(r){return this.mutations.filter(function(s){return Oe(r,s)})},i.notify=function(r){var s=this;w.batch(function(){s.listeners.forEach(function(a){a(r)})})},i.onFocus=function(){this.resumePausedMutations()},i.onOnline=function(){this.resumePausedMutations()},i.resumePausedMutations=function(){var r=this.mutations.filter(function(s){return s.state.isPaused});return w.batch(function(){return r.reduce(function(s,a){return s.then(function(){return a.continue().catch(C)})},Promise.resolve())})},t}(se);function mn(){return{onFetch:function(t){t.fetchFn=function(){var i,e,r,s,a,o,c=(i=t.fetchOptions)==null||(e=i.meta)==null?void 0:e.refetchPage,l=(r=t.fetchOptions)==null||(s=r.meta)==null?void 0:s.fetchMore,d=l==null?void 0:l.pageParam,h=(l==null?void 0:l.direction)==="forward",m=(l==null?void 0:l.direction)==="backward",f=((a=t.state.data)==null?void 0:a.pages)||[],g=((o=t.state.data)==null?void 0:o.pageParams)||[],x=Te(),P=x==null?void 0:x.signal,O=g,b=!1,y=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},q=function(D,T,F,H){return O=H?[T].concat(O):[].concat(O,[T]),H?[F].concat(D):[].concat(D,[F])},K=function(D,T,F,H){if(b)return Promise.reject("Cancelled");if(typeof F>"u"&&!T&&D.length)return Promise.resolve(D);var M={queryKey:t.queryKey,signal:P,pageParam:F,meta:t.meta},L=y(M),W=Promise.resolve(L).then(function(Ve){return q(D,F,Ve,H)});if(ee(L)){var oe=W;oe.cancel=L.cancel}return W},E;if(!f.length)E=K([]);else if(h){var ye=typeof d<"u",We=ye?d:Fe(t.options,f);E=K(f,ye,We)}else if(m){var ge=typeof d<"u",Ge=ge?d:vn(t.options,f);E=K(f,ge,Ge,!0)}else(function(){O=[];var R=typeof t.options.getNextPageParam>"u",D=c&&f[0]?c(f[0],0,f):!0;E=D?K([],R,g[0]):Promise.resolve(q([],g[0],f[0]));for(var T=function(M){E=E.then(function(L){var W=c&&f[M]?c(f[M],M,f):!0;if(W){var oe=R?g[M]:Fe(t.options,L);return K(L,R,oe)}return Promise.resolve(q(L,g[M],f[M]))})},F=1;F<f.length;F++)T(F)})();var xe=E.then(function(R){return{pages:R,pageParams:O}}),ze=xe;return ze.cancel=function(){b=!0,x==null||x.abort(),ee(E)&&E.cancel()},xe}}}}function Fe(n,t){return n.getNextPageParam==null?void 0:n.getNextPageParam(t[t.length-1],t)}function vn(n,t){return n.getPreviousPageParam==null?void 0:n.getPreviousPageParam(t[0],t)}var yn=function(){function n(i){i===void 0&&(i={}),this.queryCache=i.queryCache||new ln,this.mutationCache=i.mutationCache||new pn,this.defaultOptions=i.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=n.prototype;return t.mount=function(){var e=this;this.unsubscribeFocus=V.subscribe(function(){V.isFocused()&&Z.isOnline()&&(e.mutationCache.onFocus(),e.queryCache.onFocus())}),this.unsubscribeOnline=Z.subscribe(function(){V.isFocused()&&Z.isOnline()&&(e.mutationCache.onOnline(),e.queryCache.onOnline())})},t.unmount=function(){var e,r;(e=this.unsubscribeFocus)==null||e.call(this),(r=this.unsubscribeOnline)==null||r.call(this)},t.isFetching=function(e,r){var s=S(e,r),a=s[0];return a.fetching=!0,this.queryCache.findAll(a).length},t.isMutating=function(e){return this.mutationCache.findAll(v({},e,{fetching:!0})).length},t.getQueryData=function(e,r){var s;return(s=this.queryCache.find(e,r))==null?void 0:s.state.data},t.getQueriesData=function(e){return this.getQueryCache().findAll(e).map(function(r){var s=r.queryKey,a=r.state,o=a.data;return[s,o]})},t.setQueryData=function(e,r,s){var a=ue(e),o=this.defaultQueryOptions(a);return this.queryCache.build(this,o).setData(r,s)},t.setQueriesData=function(e,r,s){var a=this;return w.batch(function(){return a.getQueryCache().findAll(e).map(function(o){var c=o.queryKey;return[c,a.setQueryData(c,r,s)]})})},t.getQueryState=function(e,r){var s;return(s=this.queryCache.find(e,r))==null?void 0:s.state},t.removeQueries=function(e,r){var s=S(e,r),a=s[0],o=this.queryCache;w.batch(function(){o.findAll(a).forEach(function(c){o.remove(c)})})},t.resetQueries=function(e,r,s){var a=this,o=S(e,r,s),c=o[0],l=o[1],d=this.queryCache,h=v({},c,{active:!0});return w.batch(function(){return d.findAll(c).forEach(function(m){m.reset()}),a.refetchQueries(h,l)})},t.cancelQueries=function(e,r,s){var a=this,o=S(e,r,s),c=o[0],l=o[1],d=l===void 0?{}:l;typeof d.revert>"u"&&(d.revert=!0);var h=w.batch(function(){return a.queryCache.findAll(c).map(function(m){return m.cancel(d)})});return Promise.all(h).then(C).catch(C)},t.invalidateQueries=function(e,r,s){var a,o,c,l=this,d=S(e,r,s),h=d[0],m=d[1],f=v({},h,{active:(a=(o=h.refetchActive)!=null?o:h.active)!=null?a:!0,inactive:(c=h.refetchInactive)!=null?c:!1});return w.batch(function(){return l.queryCache.findAll(h).forEach(function(g){g.invalidate()}),l.refetchQueries(f,m)})},t.refetchQueries=function(e,r,s){var a=this,o=S(e,r,s),c=o[0],l=o[1],d=w.batch(function(){return a.queryCache.findAll(c).map(function(m){return m.fetch(void 0,v({},l,{meta:{refetchPage:c==null?void 0:c.refetchPage}}))})}),h=Promise.all(d).then(C);return l!=null&&l.throwOnError||(h=h.catch(C)),h},t.fetchQuery=function(e,r,s){var a=ue(e,r,s),o=this.defaultQueryOptions(a);typeof o.retry>"u"&&(o.retry=!1);var c=this.queryCache.build(this,o);return c.isStaleByTime(o.staleTime)?c.fetch(o):Promise.resolve(c.state.data)},t.prefetchQuery=function(e,r,s){return this.fetchQuery(e,r,s).then(C).catch(C)},t.fetchInfiniteQuery=function(e,r,s){var a=ue(e,r,s);return a.behavior=mn(),this.fetchQuery(a)},t.prefetchInfiniteQuery=function(e,r,s){return this.fetchInfiniteQuery(e,r,s).then(C).catch(C)},t.cancelMutations=function(){var e=this,r=w.batch(function(){return e.mutationCache.getAll().map(function(s){return s.cancel()})});return Promise.all(r).then(C).catch(C)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(e){return this.mutationCache.build(this,e).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(e){this.defaultOptions=e},t.setQueryDefaults=function(e,r){var s=this.queryDefaults.find(function(a){return I(e)===I(a.queryKey)});s?s.defaultOptions=r:this.queryDefaults.push({queryKey:e,defaultOptions:r})},t.getQueryDefaults=function(e){var r;return e?(r=this.queryDefaults.find(function(s){return X(e,s.queryKey)}))==null?void 0:r.defaultOptions:void 0},t.setMutationDefaults=function(e,r){var s=this.mutationDefaults.find(function(a){return I(e)===I(a.mutationKey)});s?s.defaultOptions=r:this.mutationDefaults.push({mutationKey:e,defaultOptions:r})},t.getMutationDefaults=function(e){var r;return e?(r=this.mutationDefaults.find(function(s){return X(e,s.mutationKey)}))==null?void 0:r.defaultOptions:void 0},t.defaultQueryOptions=function(e){if(e!=null&&e._defaulted)return e;var r=v({},this.defaultOptions.queries,this.getQueryDefaults(e==null?void 0:e.queryKey),e,{_defaulted:!0});return!r.queryHash&&r.queryKey&&(r.queryHash=ve(r.queryKey,r)),r},t.defaultQueryObserverOptions=function(e){return this.defaultQueryOptions(e)},t.defaultMutationOptions=function(e){return e!=null&&e._defaulted?e:v({},this.defaultOptions.mutations,this.getMutationDefaults(e==null?void 0:e.mutationKey),e,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},n}(),gn=Ye.unstable_batchedUpdates;w.setBatchNotifyFunction(gn);var xn=console;un(xn);var Ne=U.createContext(void 0),bn=U.createContext(!1);function jn(n){return n&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=Ne),window.ReactQueryClientContext):Ne}var wn=function(t){var i=t.client,e=t.contextSharing,r=e===void 0?!1:e,s=t.children;U.useEffect(function(){return i.mount(),function(){i.unmount()}},[i]);var a=jn(r);return U.createElement(bn.Provider,{value:r},U.createElement(a.Provider,{value:i},s))};function On({title:n,titleId:t,...i},e){return p.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":t},i),n?p.createElement("title",{id:t},n):null,p.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))}const Cn=p.forwardRef(On),Pn=Cn;function Fn({title:n,titleId:t,...i},e){return p.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":t},i),n?p.createElement("title",{id:t},n):null,p.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),p.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Nn=p.forwardRef(Fn),qn=Nn;function En({title:n,titleId:t,...i},e){return p.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":t},i),n?p.createElement("title",{id:t},n):null,p.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const _n=p.forwardRef(En),An=_n;function Dn({title:n,titleId:t,...i},e){return p.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":t},i),n?p.createElement("title",{id:t},n):null,p.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))}const Mn=p.forwardRef(Dn),Sn=Mn,$n=[{name:"Home",href:"/",icon:Sn},{name:"Chat",href:"/chat",icon:Pn},{name:"Documents",href:"/documents",icon:An},{name:"Admin",href:"/admin",icon:qn}];function Qn(){const n=Je();return u.jsxs("div",{className:"min-h-screen bg-gray-50",children:[u.jsx("nav",{className:"bg-white shadow-sm border-b border-gray-200",children:u.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:u.jsxs("div",{className:"flex justify-between h-16",children:[u.jsxs("div",{className:"flex",children:[u.jsx("div",{className:"flex-shrink-0 flex items-center",children:u.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"iChat AI"})}),u.jsx("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:$n.map(t=>{const i=n.pathname===t.href||t.href!=="/"&&n.pathname.startsWith(t.href);return u.jsxs(Xe,{to:t.href,className:`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${i?"border-blue-500 text-gray-900":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"}`,children:[u.jsx(t.icon,{className:"w-4 h-4 mr-2"}),t.name]},t.name)})})]}),u.jsx("div",{className:"flex items-center",children:u.jsx("button",{className:"btn-ghost",children:"Profile"})})]})})}),u.jsx("main",{className:"flex-1",children:u.jsx("div",{className:"py-6",children:u.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:u.jsx(et,{})})})})]})}function Rn(){return u.jsxs("div",{className:"max-w-6xl mx-auto py-8",children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Administration"}),u.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[u.jsxs("div",{className:"card",children:[u.jsx("div",{className:"card-header",children:u.jsx("h3",{className:"text-lg font-semibold",children:"User Management"})}),u.jsx("div",{className:"card-content",children:u.jsx("p",{className:"text-gray-600",children:"Manage users, roles, and permissions"})})]}),u.jsxs("div",{className:"card",children:[u.jsx("div",{className:"card-header",children:u.jsx("h3",{className:"text-lg font-semibold",children:"Escalated Queries"})}),u.jsx("div",{className:"card-content",children:u.jsx("p",{className:"text-gray-600",children:"Review and respond to escalated queries"})})]}),u.jsxs("div",{className:"card",children:[u.jsx("div",{className:"card-header",children:u.jsx("h3",{className:"text-lg font-semibold",children:"System Metrics"})}),u.jsx("div",{className:"card-content",children:u.jsx("p",{className:"text-gray-600",children:"Monitor system performance and usage"})})]}),u.jsxs("div",{className:"card",children:[u.jsx("div",{className:"card-header",children:u.jsx("h3",{className:"text-lg font-semibold",children:"Configuration"})}),u.jsx("div",{className:"card-content",children:u.jsx("p",{className:"text-gray-600",children:"Configure AI models and system settings"})})]})]})]})}function qe(){return u.jsx("div",{className:"flex flex-col h-full",children:u.jsx("div",{className:"flex-1 p-4",children:u.jsxs("div",{className:"max-w-4xl mx-auto",children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Chat"}),u.jsx("div",{className:"card h-96",children:u.jsx("div",{className:"card-content flex items-center justify-center h-full",children:u.jsx("p",{className:"text-gray-500",children:"Chat interface will be implemented here"})})}),u.jsx("div",{className:"mt-4",children:u.jsxs("div",{className:"flex gap-2",children:[u.jsx("input",{type:"text",placeholder:"Type your message...",className:"input flex-1"}),u.jsx("button",{className:"btn-primary",children:"Send"})]})})]})})})}function Ln(){return u.jsxs("div",{className:"max-w-6xl mx-auto py-8",children:[u.jsxs("div",{className:"flex justify-between items-center mb-6",children:[u.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Documents"}),u.jsx("button",{className:"btn-primary",children:"Upload Document"})]}),u.jsx("div",{className:"card",children:u.jsx("div",{className:"card-content",children:u.jsx("div",{className:"text-center py-12",children:u.jsx("p",{className:"text-gray-500",children:"Document management interface will be implemented here"})})})})]})}function kn(){return u.jsx("div",{className:"max-w-4xl mx-auto py-8",children:u.jsxs("div",{className:"text-center",children:[u.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Welcome to iChat AI Assistant"}),u.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"Your intelligent internal chat agent for document-based Q&A"}),u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12",children:[u.jsxs("div",{className:"card",children:[u.jsx("div",{className:"card-header",children:u.jsx("h3",{className:"text-lg font-semibold",children:"Smart Chat"})}),u.jsx("div",{className:"card-content",children:u.jsx("p",{className:"text-gray-600",children:"Ask questions and get intelligent responses powered by AI and your company's knowledge base."})})]}),u.jsxs("div",{className:"card",children:[u.jsx("div",{className:"card-header",children:u.jsx("h3",{className:"text-lg font-semibold",children:"Document Processing"})}),u.jsx("div",{className:"card-content",children:u.jsx("p",{className:"text-gray-600",children:"Upload and process documents to expand the AI's knowledge and improve response accuracy."})})]}),u.jsxs("div",{className:"card",children:[u.jsx("div",{className:"card-header",children:u.jsx("h3",{className:"text-lg font-semibold",children:"Human Escalation"})}),u.jsx("div",{className:"card-content",children:u.jsx("p",{className:"text-gray-600",children:"When the AI isn't confident, queries are escalated to human experts for review and learning."})})]})]})]})})}function In(){return u.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:u.jsxs("div",{className:"max-w-md w-full space-y-8",children:[u.jsxs("div",{children:[u.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to iChat AI Assistant"}),u.jsx("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Access your intelligent chat agent"})]}),u.jsx("div",{className:"card",children:u.jsx("div",{className:"card-content",children:u.jsxs("form",{className:"space-y-6",children:[u.jsxs("div",{children:[u.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),u.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"input mt-1",placeholder:"Enter your email"})]}),u.jsxs("div",{children:[u.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),u.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"input mt-1",placeholder:"Enter your password"})]}),u.jsx("div",{children:u.jsx("button",{type:"submit",className:"btn-primary w-full",children:"Sign in"})})]})})})]})})}function Tn(){return u.jsx("div",{className:"min-h-screen bg-gray-50",children:u.jsxs(tt,{children:[u.jsx(k,{path:"/login",element:u.jsx(In,{})}),u.jsxs(k,{path:"/",element:u.jsx(Qn,{}),children:[u.jsx(k,{index:!0,element:u.jsx(kn,{})}),u.jsx(k,{path:"chat",element:u.jsx(qe,{})}),u.jsx(k,{path:"chat/:sessionId",element:u.jsx(qe,{})}),u.jsx(k,{path:"documents",element:u.jsx(Ln,{})}),u.jsx(k,{path:"admin",element:u.jsx(Rn,{})})]})]})})}const Un=new yn({defaultOptions:{queries:{retry:1,refetchOnWindowFocus:!1}}});le.createRoot(document.getElementById("root")).render(u.jsx(U.StrictMode,{children:u.jsx(wn,{client:Un,children:u.jsxs(nt,{children:[u.jsx(Tn,{}),u.jsx(Zt,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"}}})]})})}));
//# sourceMappingURL=index-ed3c57e0.js.map
