#!/usr/bin/env node

import chalk from 'chalk';
import { Command } from 'commander';

import { AuthCommand } from './commands/auth';
import { ChatCommand } from './commands/chat';
import { ConfigCommand } from './commands/config';
import { DocsCommand } from './commands/docs';
import { QueriesCommand } from './commands/queries';
import { SystemCommand } from './commands/system';
import { UsersCommand } from './commands/users';

const program = new Command();

// CLI metadata
program
  .name('ichat-cli')
  .description('iChat AI Assistant CLI Tool')
  .version('1.0.0')
  .configureOutput({
    outputError: (str, write) => write(chalk.red(str)),
  });

// Global options
program
  .option('-v, --verbose', 'enable verbose logging')
  .option('--api-url <url>', 'API base URL')
  .option('--timeout <ms>', 'request timeout in milliseconds')
  .option('--format <format>', 'output format (json|table|yaml)', 'table');

// Register command modules
try {
  program.addCommand(new AuthCommand().getCommand());
  program.addCommand(new DocsCommand().getCommand());
  program.addCommand(new UsersCommand().getCommand());
  program.addCommand(new ChatCommand().getCommand());
  program.addCommand(new QueriesCommand().getCommand());
  program.addCommand(new SystemCommand().getCommand());
  program.addCommand(new ConfigCommand().getCommand());
} catch (error) {
  console.error(chalk.red('Error loading commands:'), error);
  process.exit(1);
}

// Error handling
program.exitOverride();

try {
  program.parse();
} catch (error) {
  if (error instanceof Error) {
    console.error(chalk.red('CLI Error:'), error.message);
  }
  process.exit(1);
}

// Show help if no command provided
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
