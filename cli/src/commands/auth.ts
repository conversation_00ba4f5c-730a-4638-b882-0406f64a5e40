import chalk from 'chalk';
import { Command } from 'commander';
import inquirer from 'inquirer';

import { ConfigManager } from '../config';

export class AuthCommand {
  private config: ConfigManager;

  constructor() {
    this.config = new ConfigManager();
  }

  public getCommand(): Command {
    const authCommand = new Command('auth');
    authCommand.description('Authentication commands');

    // Login command
    authCommand
      .command('login')
      .description('Authenticate with the iChat API')
      .option('-k, --api-key <key>', 'API key for authentication')
      .action(async (options) => {
        try {
          await this.login(options);
        } catch (error) {
          console.error(chalk.red('Login failed:'), error);
          process.exit(1);
        }
      });

    // Logout command
    authCommand
      .command('logout')
      .description('Clear authentication credentials')
      .action(async () => {
        try {
          await this.logout();
        } catch (error) {
          console.error(chalk.red('Logout failed:'), error);
          process.exit(1);
        }
      });

    // Status command
    authCommand
      .command('status')
      .description('Show authentication status')
      .action(async () => {
        try {
          await this.status();
        } catch (error) {
          console.error(chalk.red('Status check failed:'), error);
          process.exit(1);
        }
      });

    return authCommand;
  }

  private async login(options: { apiKey?: string }): Promise<void> {
    let {apiKey} = options;

    if (!apiKey) {
      const answers = await inquirer.prompt([
        {
          type: 'password',
          name: 'apiKey',
          message: 'Enter your API key:',
          mask: '*',
          validate: (input: string) => {
            if (!input.trim()) {
              return 'API key is required';
            }
            return true;
          },
        },
      ]);
      apiKey = answers.apiKey;
    }

    // Store API key
    this.config.set('apiKey', apiKey);

    console.log(chalk.green('✓ Successfully authenticated'));
    console.log(chalk.gray(`API URL: ${this.config.get('apiUrl')}`));
  }

  private async logout(): Promise<void> {
    this.config.set('apiKey', undefined);
    console.log(chalk.green('✓ Successfully logged out'));
  }

  private async status(): Promise<void> {
    const apiKey = this.config.get('apiKey');
    const apiUrl = this.config.get('apiUrl');

    if (apiKey) {
      console.log(chalk.green('✓ Authenticated'));
      console.log(chalk.gray(`API URL: ${apiUrl}`));
      console.log(chalk.gray(`API Key: ${apiKey.substring(0, 8)}...`));
    } else {
      console.log(chalk.yellow('⚠ Not authenticated'));
      console.log(chalk.gray('Run "ichat-cli auth login" to authenticate'));
    }
  }
}
