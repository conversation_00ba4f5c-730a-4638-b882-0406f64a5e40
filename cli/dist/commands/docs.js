"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocsCommand = void 0;
const commander_1 = require("commander");
class DocsCommand {
    getCommand() {
        const docsCommand = new commander_1.Command('docs');
        docsCommand.description('Document management commands');
        docsCommand
            .command('upload <file>')
            .description('Upload a document')
            .action((file) => {
            console.log(`Uploading document: ${file}`);
        });
        docsCommand
            .command('list')
            .description('List all documents')
            .action(() => {
            console.log('Listing documents...');
        });
        docsCommand
            .command('delete <id>')
            .description('Delete a document')
            .action((id) => {
            console.log(`Deleting document: ${id}`);
        });
        return docsCommand;
    }
}
exports.DocsCommand = DocsCommand;
//# sourceMappingURL=docs.js.map