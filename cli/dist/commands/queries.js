"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueriesCommand = void 0;
const commander_1 = require("commander");
class QueriesCommand {
    getCommand() {
        const queriesCommand = new commander_1.Command('queries');
        queriesCommand.description('Escalated query management commands');
        queriesCommand
            .command('list')
            .description('List escalated queries')
            .option('-s, --status <status>', 'Filter by status')
            .action((options) => {
            console.log('Listing escalated queries...');
            if (options.status) {
                console.log(`Filtering by status: ${options.status}`);
            }
        });
        queriesCommand
            .command('respond <id>')
            .description('Respond to an escalated query')
            .action((id) => {
            console.log(`Responding to query: ${id}`);
        });
        return queriesCommand;
    }
}
exports.QueriesCommand = QueriesCommand;
//# sourceMappingURL=queries.js.map