"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemCommand = void 0;
const commander_1 = require("commander");
class SystemCommand {
    getCommand() {
        const systemCommand = new commander_1.Command('system');
        systemCommand.description('System administration commands');
        systemCommand
            .command('status')
            .description('Check system health')
            .action(() => {
            console.log('Checking system status...');
        });
        systemCommand
            .command('metrics')
            .description('Show system metrics')
            .action(() => {
            console.log('Fetching system metrics...');
        });
        return systemCommand;
    }
}
exports.SystemCommand = SystemCommand;
//# sourceMappingURL=system.js.map