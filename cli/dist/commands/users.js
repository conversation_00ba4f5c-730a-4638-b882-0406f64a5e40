"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersCommand = void 0;
const commander_1 = require("commander");
class UsersCommand {
    getCommand() {
        const usersCommand = new commander_1.Command('users');
        usersCommand.description('User management commands');
        usersCommand
            .command('create <email>')
            .description('Create a new user')
            .option('-n, --name <name>', 'User name')
            .option('-r, --role <role>', 'User role (user|admin|manager)', 'user')
            .action((email, options) => {
            console.log(`Creating user: ${email}`);
            console.log(`Name: ${options.name || 'Not specified'}`);
            console.log(`Role: ${options.role}`);
        });
        usersCommand
            .command('list')
            .description('List all users')
            .action(() => {
            console.log('Listing users...');
        });
        return usersCommand;
    }
}
exports.UsersCommand = UsersCommand;
//# sourceMappingURL=users.js.map