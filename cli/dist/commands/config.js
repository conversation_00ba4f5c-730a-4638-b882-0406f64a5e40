"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const commander_1 = require("commander");
const config_1 = require("../config");
class ConfigCommand {
    config;
    constructor() {
        this.config = new config_1.ConfigManager();
    }
    getCommand() {
        const configCommand = new commander_1.Command('config');
        configCommand.description('Configuration management commands');
        configCommand
            .command('get [key]')
            .description('Get configuration value(s)')
            .action((key) => {
            if (key) {
                const value = this.config.get(key);
                console.log(`${key}: ${value}`);
            }
            else {
                const allConfig = this.config.getAll();
                Object.entries(allConfig).forEach(([k, v]) => {
                    if (k === 'apiKey' && v) {
                        console.log(`${k}: ${String(v).substring(0, 8)}...`);
                    }
                    else {
                        console.log(`${k}: ${v}`);
                    }
                });
            }
        });
        configCommand
            .command('set <key> <value>')
            .description('Set configuration value')
            .action((key, value) => {
            try {
                this.config.set(key, value);
                console.log(chalk_1.default.green(`✓ Set ${key} = ${value}`));
            }
            catch (error) {
                console.error(chalk_1.default.red(`Failed to set ${key}:`), error);
            }
        });
        return configCommand;
    }
}
exports.ConfigCommand = ConfigCommand;
//# sourceMappingURL=config.js.map