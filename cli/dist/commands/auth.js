"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const commander_1 = require("commander");
const inquirer_1 = __importDefault(require("inquirer"));
const config_1 = require("../config");
class AuthCommand {
    config;
    constructor() {
        this.config = new config_1.ConfigManager();
    }
    getCommand() {
        const authCommand = new commander_1.Command('auth');
        authCommand.description('Authentication commands');
        authCommand
            .command('login')
            .description('Authenticate with the iChat API')
            .option('-k, --api-key <key>', 'API key for authentication')
            .action(async (options) => {
            try {
                await this.login(options);
            }
            catch (error) {
                console.error(chalk_1.default.red('Login failed:'), error);
                process.exit(1);
            }
        });
        authCommand
            .command('logout')
            .description('Clear authentication credentials')
            .action(async () => {
            try {
                await this.logout();
            }
            catch (error) {
                console.error(chalk_1.default.red('Logout failed:'), error);
                process.exit(1);
            }
        });
        authCommand
            .command('status')
            .description('Show authentication status')
            .action(async () => {
            try {
                await this.status();
            }
            catch (error) {
                console.error(chalk_1.default.red('Status check failed:'), error);
                process.exit(1);
            }
        });
        return authCommand;
    }
    async login(options) {
        let { apiKey } = options;
        if (!apiKey) {
            const answers = await inquirer_1.default.prompt([
                {
                    type: 'password',
                    name: 'apiKey',
                    message: 'Enter your API key:',
                    mask: '*',
                    validate: (input) => {
                        if (!input.trim()) {
                            return 'API key is required';
                        }
                        return true;
                    },
                },
            ]);
            apiKey = answers.apiKey;
        }
        this.config.set('apiKey', apiKey);
        console.log(chalk_1.default.green('✓ Successfully authenticated'));
        console.log(chalk_1.default.gray(`API URL: ${this.config.get('apiUrl')}`));
    }
    async logout() {
        this.config.set('apiKey', undefined);
        console.log(chalk_1.default.green('✓ Successfully logged out'));
    }
    async status() {
        const apiKey = this.config.get('apiKey');
        const apiUrl = this.config.get('apiUrl');
        if (apiKey) {
            console.log(chalk_1.default.green('✓ Authenticated'));
            console.log(chalk_1.default.gray(`API URL: ${apiUrl}`));
            console.log(chalk_1.default.gray(`API Key: ${apiKey.substring(0, 8)}...`));
        }
        else {
            console.log(chalk_1.default.yellow('⚠ Not authenticated'));
            console.log(chalk_1.default.gray('Run "ichat-cli auth login" to authenticate'));
        }
    }
}
exports.AuthCommand = AuthCommand;
//# sourceMappingURL=auth.js.map