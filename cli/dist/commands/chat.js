"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatCommand = void 0;
const commander_1 = require("commander");
class ChatCommand {
    getCommand() {
        const chatCommand = new commander_1.Command('chat');
        chatCommand.description('Interactive chat commands');
        chatCommand
            .command('start')
            .description('Start an interactive chat session')
            .action(() => {
            console.log('Starting interactive chat...');
            console.log('Type "exit" to quit');
        });
        return chatCommand;
    }
}
exports.ChatCommand = ChatCommand;
//# sourceMappingURL=chat.js.map