#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const chalk_1 = __importDefault(require("chalk"));
const commander_1 = require("commander");
const auth_1 = require("./commands/auth");
const chat_1 = require("./commands/chat");
const config_1 = require("./commands/config");
const docs_1 = require("./commands/docs");
const queries_1 = require("./commands/queries");
const system_1 = require("./commands/system");
const users_1 = require("./commands/users");
const program = new commander_1.Command();
program
    .name('ichat-cli')
    .description('iChat AI Assistant CLI Tool')
    .version('1.0.0')
    .configureOutput({
    outputError: (str, write) => write(chalk_1.default.red(str)),
});
program
    .option('-v, --verbose', 'enable verbose logging')
    .option('--api-url <url>', 'API base URL')
    .option('--timeout <ms>', 'request timeout in milliseconds')
    .option('--format <format>', 'output format (json|table|yaml)', 'table');
try {
    program.addCommand(new auth_1.AuthCommand().getCommand());
    program.addCommand(new docs_1.DocsCommand().getCommand());
    program.addCommand(new users_1.UsersCommand().getCommand());
    program.addCommand(new chat_1.ChatCommand().getCommand());
    program.addCommand(new queries_1.QueriesCommand().getCommand());
    program.addCommand(new system_1.SystemCommand().getCommand());
    program.addCommand(new config_1.ConfigCommand().getCommand());
}
catch (error) {
    console.error(chalk_1.default.red('Error loading commands:'), error);
    process.exit(1);
}
program.exitOverride();
try {
    program.parse();
}
catch (error) {
    if (error instanceof Error) {
        console.error(chalk_1.default.red('CLI Error:'), error.message);
    }
    process.exit(1);
}
if (!process.argv.slice(2).length) {
    program.outputHelp();
}
//# sourceMappingURL=index.js.map