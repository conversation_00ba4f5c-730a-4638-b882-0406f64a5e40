interface CLIConfig {
    apiUrl: string;
    apiKey?: string;
    timeout: number;
    format: 'json' | 'table' | 'yaml';
}
export declare class ConfigManager {
    private configPath;
    private config;
    constructor();
    private loadConfig;
    get<K extends keyof CLIConfig>(key: K): CLIConfig[K];
    set<K extends keyof CLIConfig>(key: K, value: CLIConfig[K]): void;
    getAll(): CLIConfig;
    private saveConfig;
    isAuthenticated(): boolean;
}
export {};
//# sourceMappingURL=index.d.ts.map