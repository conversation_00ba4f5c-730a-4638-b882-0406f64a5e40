"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const fs_1 = __importDefault(require("fs"));
const os_1 = __importDefault(require("os"));
const path_1 = __importDefault(require("path"));
class ConfigManager {
    configPath;
    config;
    constructor() {
        this.configPath = path_1.default.join(os_1.default.homedir(), '.ichat', 'config.json');
        this.loadConfig();
    }
    loadConfig() {
        const defaultConfig = {
            apiUrl: process.env.CLI_API_URL || 'http://localhost:3000/api',
            timeout: parseInt(process.env.CLI_TIMEOUT || '30000'),
            format: 'table',
        };
        try {
            if (fs_1.default.existsSync(this.configPath)) {
                const fileConfig = JSON.parse(fs_1.default.readFileSync(this.configPath, 'utf8'));
                this.config = { ...defaultConfig, ...fileConfig };
            }
            else {
                this.config = defaultConfig;
                this.saveConfig();
            }
        }
        catch (error) {
            console.warn('Warning: Could not load config file, using defaults');
            this.config = defaultConfig;
        }
    }
    get(key) {
        return this.config[key];
    }
    set(key, value) {
        this.config[key] = value;
        this.saveConfig();
    }
    getAll() {
        return { ...this.config };
    }
    saveConfig() {
        try {
            const configDir = path_1.default.dirname(this.configPath);
            if (!fs_1.default.existsSync(configDir)) {
                fs_1.default.mkdirSync(configDir, { recursive: true });
            }
            fs_1.default.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
        }
        catch (error) {
            console.warn('Warning: Could not save config file');
        }
    }
    isAuthenticated() {
        return !!this.config.apiKey;
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=index.js.map